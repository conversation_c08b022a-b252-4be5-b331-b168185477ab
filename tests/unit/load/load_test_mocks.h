// Shared mock implementations and test utilities for load module unit tests

#pragma once

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/interfaces.h"
#include "core/record.h"
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include <memory>
#include <thread>
#include <chrono>
#include <filesystem>
#include <fstream>

namespace omop::load::test {

using ::testing::_;
using ::testing::Return;
using ::testing::Invoke;
using ::testing::NiceMock;

// Test utilities
class TestUtils {
public:
    // Create a test directory and clean it up on destruction
    class TempDirectory {
    public:
        explicit TempDirectory(const std::string& prefix = "test_") {
            path_ = std::filesystem::temp_directory_path() / 
                    (prefix + std::to_string(std::chrono::system_clock::now()
                        .time_since_epoch().count()));
            std::filesystem::create_directories(path_);
        }
        
        ~TempDirectory() {
            std::error_code ec;
            std::filesystem::remove_all(path_, ec);
        }
        
        std::string path() const { return path_.string(); }
        
    private:
        std::filesystem::path path_;
    };
    
    // Create test records with various data types
    static core::Record create_test_record(int id) {
        core::Record record;
        record.setField("id", int64_t(id));
        record.setField("name", std::string("Test_" + std::to_string(id)));
        record.setField("value", double(id * 10.5));
        record.setField("active", id % 2 == 0);
        record.setField("created_at", std::chrono::system_clock::now());
        core::Record::RecordMetadata metadata;
        metadata.source_table = "unit_test";
        metadata.extraction_time = std::chrono::system_clock::now();
        record.setMetadata(metadata);
        return record;
    }
    
    // Create a batch of test records
    static core::RecordBatch create_test_batch(size_t size, int start_id = 1) {
        core::RecordBatch batch;
        for (size_t i = 0; i < size; ++i) {
            batch.addRecord(create_test_record(start_id + i));
        }
        return batch;
    }
    
    // Create OMOP-specific test records
    static core::Record create_person_record(int64_t person_id) {
        core::Record record;
        record.setField("person_id", person_id);
        record.setField("gender_concept_id", int32_t(8507)); // Male
        record.setField("year_of_birth", int32_t(1980));
        record.setField("race_concept_id", int32_t(8527)); // White
        record.setField("ethnicity_concept_id", int32_t(38003564)); // Not Hispanic
        record.setField("person_source_value", std::string("P" + std::to_string(person_id)));
        return record;
    }
    
    static core::Record create_visit_record(int64_t visit_id, int64_t person_id) {
        core::Record record;
        record.setField("visit_occurrence_id", visit_id);
        record.setField("person_id", person_id);
        record.setField("visit_concept_id", int32_t(9201)); // Inpatient Visit
        record.setField("visit_start_date", std::chrono::system_clock::now());
        record.setField("visit_end_date", 
            std::chrono::system_clock::now() + std::chrono::hours(24));
        record.setField("visit_type_concept_id", int32_t(44818517)); // Visit derived from encounter
        return record;
    }
    
    // Wait for async operations with timeout
    template<typename Predicate>
    static bool wait_for(Predicate pred, std::chrono::milliseconds timeout = std::chrono::milliseconds(1000)) {
        auto start = std::chrono::steady_clock::now();
        while (!pred()) {
            if (std::chrono::steady_clock::now() - start > timeout) {
                return false;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        return true;
    }
};

// Mock implementations for testing

// Enhanced mock database connection with common behavior
class MockDatabaseConnectionBase : public extract::IDatabaseConnection {
public:
    MockDatabaseConnectionBase() {
        // Set up default behavior
        ON_CALL(*this, is_connected()).WillByDefault(Return(true));
        ON_CALL(*this, get_database_type()).WillByDefault(Return("mock"));
        ON_CALL(*this, table_exists(_, _)).WillByDefault(Return(true));
    }
    
    MOCK_METHOD(void, connect, (const extract::IDatabaseConnection::ConnectionParams&), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD(std::unique_ptr<extract::IResultSet>, execute_query, (const std::string&), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string&), (override));
    MOCK_METHOD(std::unique_ptr<extract::IPreparedStatement>, prepare_statement, (const std::string&), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(bool, table_exists, (const std::string&, const std::string&), (const, override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int), (override));
};

// Mock prepared statement with parameter tracking
class MockPreparedStatementWithTracking : public extract::IPreparedStatement {
public:
    MOCK_METHOD(void, bind, (size_t, const std::any&), (override));
    MOCK_METHOD(std::unique_ptr<extract::IResultSet>, execute_query, (), (override));
    MOCK_METHOD(size_t, execute_update, (), (override));
    MOCK_METHOD(void, clear_parameters, (), (override));
    
    // Track bound parameters for verification
    std::vector<std::pair<int, std::any>> bound_parameters;
    
    MockPreparedStatementWithTracking() {
        ON_CALL(*this, bind(_, _)).WillByDefault(
            [this](size_t index, const std::any& value) {
                bound_parameters.push_back({static_cast<int>(index), value});
            });
        
        ON_CALL(*this, clear_parameters()).WillByDefault(
            [this]() { bound_parameters.clear(); });
    }
};

// Mock result set with data
class MockResultSetWithData : public extract::IResultSet {
public:
    MOCK_METHOD(bool, next, (), (override));
    MOCK_METHOD(std::any, get_value, (size_t), (const, override));
    MOCK_METHOD(std::any, get_value, (const std::string&), (const, override));
    MOCK_METHOD(bool, is_null, (size_t), (const, override));
    MOCK_METHOD(bool, is_null, (const std::string&), (const, override));
    MOCK_METHOD(size_t, column_count, (), (const, override));
    MOCK_METHOD(std::string, column_name, (size_t), (const, override));
    MOCK_METHOD(std::string, column_type, (size_t), (const, override));
    MOCK_METHOD(core::Record, to_record, (), (const, override));
    
    // Helper to set up data
    void add_row(const std::vector<std::any>& row) {
        data_.push_back(row);
    }
    
    void setup_sequential_data() {
        current_row_ = -1;
        ON_CALL(*this, next()).WillByDefault([this]() {
            current_row_++;
            return current_row_ < static_cast<int>(data_.size());
        });
        
        ON_CALL(*this, get_value(testing::An<size_t>())).WillByDefault(
            [this](size_t index) -> std::any {
                if (current_row_ >= 0 && current_row_ < static_cast<int>(data_.size()) &&
                    index < data_[current_row_].size()) {
                    return data_[current_row_][index];
                }
                return std::any{};
            });
    }
    
private:
    std::vector<std::vector<std::any>> data_;
    int current_row_ = -1;
};

// Mock loader with behavior tracking
class MockLoaderWithTracking : public core::ILoader {
public:
    MOCK_METHOD(void, initialize, ((const std::unordered_map<std::string, std::any>&), (core::ProcessingContext&)), (override));
    MOCK_METHOD(bool, load, (const core::Record&, core::ProcessingContext&), (override));
    MOCK_METHOD(size_t, load_batch, (const core::RecordBatch&, core::ProcessingContext&), (override));
    MOCK_METHOD(void, commit, (core::ProcessingContext&), (override));
    MOCK_METHOD(void, rollback, (core::ProcessingContext&), (override));
    MOCK_METHOD(std::string, get_type, (), (const, override));
    MOCK_METHOD(void, finalize, (core::ProcessingContext&), (override));
    MOCK_METHOD((std::unordered_map<std::string, std::any>), get_statistics, (), (const, override));
    
    // Track loaded records
    std::vector<core::Record> loaded_records;
    size_t total_loaded = 0;
    size_t total_failed = 0;
    bool initialized = false;
    bool finalized = false;
    
    MockLoaderWithTracking() {
        ON_CALL(*this, initialize(_, _)).WillByDefault(
            [this](const auto&, auto&) { initialized = true; });
        
        ON_CALL(*this, load(_, _)).WillByDefault(
            [this](const core::Record& record, auto&) {
                loaded_records.push_back(record);
                total_loaded++;
                return true;
            });
        
        ON_CALL(*this, load_batch(_, _)).WillByDefault(
            [this](const core::RecordBatch& batch, auto&) {
                for (const auto& record : batch) {
                    loaded_records.push_back(record);
                }
                total_loaded += batch.size();
                return batch.size();
            });
        
        ON_CALL(*this, finalize(_)).WillByDefault(
            [this](auto&) { finalized = true; });
        
        ON_CALL(*this, get_statistics()).WillByDefault([this]() {
            return std::unordered_map<std::string, std::any>{
                {"total_loaded", total_loaded},
                {"total_failed", total_failed},
                {"record_count", loaded_records.size()}
            };
        });
    }
};

// Test fixture base class with common setup
class LoaderTestBase : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temp directory for file-based tests
        temp_dir = std::make_unique<TestUtils::TempDirectory>("loader_test_");

        // context is already initialized by default constructor

        // Set up logging for tests (simplified for testing)
        // Logger initialization is handled by the main application
    }

    void TearDown() override {
        // Cleanup is handled by TempDirectory destructor
        temp_dir.reset();
    }

    // Common test configuration
    std::unordered_map<std::string, std::any> create_basic_config() {
        return {
            {"output_directory", temp_dir->path()},
            {"batch_size", size_t(10)},
            {"use_compression", false}
        };
    }

    // Verify file contents
    bool verify_file_contains(const std::string& filepath, const std::string& content) {
        std::ifstream file(filepath);
        if (!file.is_open()) return false;

        std::string file_content((std::istreambuf_iterator<char>(file)),
                                std::istreambuf_iterator<char>());
        return file_content.find(content) != std::string::npos;
    }

    // Wait for file to be created
    bool wait_for_file(const std::string& filepath,
                      std::chrono::milliseconds timeout = std::chrono::milliseconds(1000)) {
        return TestUtils::wait_for([&filepath]() {
            return std::filesystem::exists(filepath);
        }, timeout);
    }

protected:
    std::unique_ptr<TestUtils::TempDirectory> temp_dir;
    core::ProcessingContext context;
};

// Performance testing utilities
class PerformanceTestUtils {
public:
    class Timer {
    public:
        Timer() : start_(std::chrono::high_resolution_clock::now()) {}

        double elapsed_seconds() const {
            auto end = std::chrono::high_resolution_clock::now();
            return std::chrono::duration<double>(end - start_).count();
        }

        double elapsed_milliseconds() const {
            return elapsed_seconds() * 1000.0;
        }

    private:
        std::chrono::high_resolution_clock::time_point start_;
    };

    // Measure throughput
    static double calculate_throughput(size_t record_count, double elapsed_seconds) {
        return elapsed_seconds > 0 ? record_count / elapsed_seconds : 0.0;
    }

    // Generate large dataset for performance testing
    static core::RecordBatch generate_large_dataset(size_t size) {
        core::RecordBatch batch;
        for (size_t i = 0; i < size; ++i) {
            core::Record record;
            record.setField("id", int64_t(i));
            record.setField("uuid", std::string("550e8400-e29b-41d4-a716-") +
                           std::to_string(i).substr(0, 12));
            record.setField("timestamp", std::chrono::system_clock::now());
            record.setField("data", std::string(1000, 'x')); // 1KB of data

            // Add some variety
            for (int j = 0; j < 10; ++j) {
                record.setField("field_" + std::to_string(j),
                               std::rand() % 1000);
            }

            batch.addRecord(std::move(record));
        }
        return batch;
    }
};

} // namespace omop::load::test
