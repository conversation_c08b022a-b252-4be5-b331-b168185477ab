/**
 * @file extractor_base_test.cpp
 * @brief Unit tests for extractor base class functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/extractor_base.h"
#include "common/exceptions.h"
#include "common/configuration.h"
#include "common/logging.h"
#include "core/interfaces.h"
#include <thread>
#include <chrono>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Invoke;

// Mock extractor implementation for testing
class MockExtractor : public ExtractorBase {
public:
    MockExtractor(const std::string& name,
                  std::shared_ptr<ConfigurationManager> config,
                  std::shared_ptr<Logger> logger)
        : ExtractorBase(name, config, logger) {}

    // Mock the pure virtual methods from ExtractorBase
    MOCK_METHOD(SourceSchema, getSchema, (), (const, override));
    MOCK_METHOD(ValidationResult, validateSource, (), (override));

public:
    // Mock the protected pure virtual methods - made public for testing
    MOCK_METHOD(bool, connect, (), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD((std::vector<Record>), extractBatchImpl, (size_t batch_size), (override));
    MOCK_METHOD(Record, convertToRecord, (const std::any& source_data), (override));
};

// Test fixture for extractor base tests
class ExtractorBaseTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_ = std::make_shared<ConfigurationManager>();
        logger_ = Logger::get("test-extractor");
        extractor_ = std::make_unique<MockExtractor>("test_extractor", config_, logger_);
        extractor_ptr_ = extractor_.get();
    }

    std::shared_ptr<ConfigurationManager> config_;
    std::shared_ptr<Logger> logger_;
    std::unique_ptr<MockExtractor> extractor_;
    MockExtractor* extractor_ptr_;
};

// Tests extractor initialization
TEST_F(ExtractorBaseTest, Initialize) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    EXPECT_CALL(*extractor_ptr_, connect())
        .WillOnce(Return(true));

    ValidationResult valid_result;  // Default constructor creates valid result
    EXPECT_CALL(*extractor_ptr_, validateSource())
        .WillOnce(Return(valid_result));

    EXPECT_NO_THROW(extractor_ptr_->initialize(config, context));
}

// Tests initialization failure on connection
TEST_F(ExtractorBaseTest, InitializeConnectionFailure) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    EXPECT_CALL(*extractor_ptr_, connect())
        .WillOnce(Return(false));

    EXPECT_THROW(extractor_ptr_->initialize(config, context), std::runtime_error);
}

// Tests initialization failure on validation
TEST_F(ExtractorBaseTest, InitializeValidationFailure) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    EXPECT_CALL(*extractor_ptr_, connect())
        .WillOnce(Return(true));

    ValidationResult invalid_result;
    invalid_result.add_error("schema", "Schema mismatch", "validation");
    EXPECT_CALL(*extractor_ptr_, validateSource())
        .WillOnce(Return(invalid_result));

    EXPECT_THROW(extractor_ptr_->initialize(config, context), std::runtime_error);
}

// Tests basic extraction
TEST_F(ExtractorBaseTest, Extract) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Initialize extractor
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;  // Default constructor creates valid result
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    // Set up mock batch extraction
    std::vector<Record> mock_records;
    for (int i = 1; i <= 5; ++i) {
        Record r;
        r.setField("id", i);
        r.setField("value", i * 10);
        mock_records.push_back(r);
    }

    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(10))
        .WillOnce(Return(mock_records));

    auto result = extractor_ptr_->extract_batch(10, context);

    EXPECT_EQ(5, result.size());
}

// Tests has_more_data functionality
TEST_F(ExtractorBaseTest, HasMoreData) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Before initialization
    EXPECT_FALSE(extractor_ptr_->has_more_data());

    // After initialization
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    EXPECT_TRUE(extractor_ptr_->has_more_data());
}

// Tests extraction with max records limit
TEST_F(ExtractorBaseTest, ExtractWithMaxRecords) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    // Set up to return records
    std::vector<Record> mock_batch;
    for (int i = 1; i <= 10; ++i) {
        Record r;
        r.setField("id", i);
        mock_batch.push_back(r);
    }

    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(10))
        .WillOnce(Return(mock_batch));

    auto result = extractor_ptr_->extract_batch(10, context);

    EXPECT_EQ(10, result.size());
}

// Tests extraction with column selection
TEST_F(ExtractorBaseTest, ExtractWithColumnSelection) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    // Create records with multiple fields
    std::vector<Record> mock_records;
    Record r;
    r.setField("id", 1);
    r.setField("name", std::string("test"));
    r.setField("value", 100);
    r.setField("extra", std::string("should be filtered"));
    mock_records.push_back(r);

    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(10))
        .WillOnce(Return(mock_records));

    auto result = extractor_ptr_->extract_batch(10, context);

    EXPECT_EQ(1, result.size());

    // Check that all fields are present (column filtering would be done at higher level)
    const auto& extracted_record = result.getRecord(0);
    EXPECT_TRUE(extracted_record.hasField("id"));
    EXPECT_TRUE(extracted_record.hasField("name"));
    EXPECT_TRUE(extracted_record.hasField("value"));
    EXPECT_TRUE(extracted_record.hasField("extra"));
}

// Tests batch extraction
TEST_F(ExtractorBaseTest, ExtractBatch) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    // Mock records
    std::vector<Record> mock_records;
    for (int i = 1; i <= 3; ++i) {
        Record r;
        r.setField("id", i);
        mock_records.push_back(r);
    }

    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(10))
        .WillOnce(Return(mock_records));

    auto batch = extractor_ptr_->extract_batch(10, context);
    EXPECT_EQ(3, batch.size());
}

// Tests error handling during extraction
TEST_F(ExtractorBaseTest, ExtractWithErrors) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    // Set up to throw exception during batch extraction
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(_))
        .WillOnce(::testing::Throw(std::runtime_error("Extraction error")));

    EXPECT_THROW(extractor_ptr_->extract_batch(10, context), std::runtime_error);
}

// Tests record conversion
TEST_F(ExtractorBaseTest, ConvertToRecord) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    // Test record conversion
    std::any source_data = std::make_any<int>(42);
    Record expected_record;
    expected_record.setField("id", 42);

    EXPECT_CALL(*extractor_ptr_, convertToRecord(_))
        .WillOnce(Return(expected_record));

    auto result = extractor_ptr_->convertToRecord(source_data);
    EXPECT_TRUE(result.hasField("id"));
    EXPECT_EQ(42, std::any_cast<int>(result.getField("id")));
}

// Tests get_type functionality
TEST_F(ExtractorBaseTest, GetType) {
    std::string type = extractor_ptr_->get_type();
    EXPECT_FALSE(type.empty());
}

// Tests reset functionality
TEST_F(ExtractorBaseTest, Reset) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    // Extract some data
    std::vector<Record> mock_records;
    mock_records.push_back(Record());
    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(_))
        .WillOnce(Return(mock_records));

    extractor_ptr_->extract_batch(10, context);

    // Disconnect should be called during reset
    EXPECT_CALL(*extractor_ptr_, disconnect()).Times(1);

    extractor_ptr_->reset();

    // After reset, should not have more data
    EXPECT_FALSE(extractor_ptr_->has_more_data());
}

// Tests close functionality
TEST_F(ExtractorBaseTest, Close) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    EXPECT_CALL(*extractor_ptr_, disconnect()).Times(1);

    extractor_ptr_->close();

    // Should not have more data after close
    EXPECT_FALSE(extractor_ptr_->has_more_data());
}

// Tests schema retrieval
TEST_F(ExtractorBaseTest, GetSchema) {
    SourceSchema schema;
    schema.source_name = "test_source";
    schema.source_type = "test";

    SourceSchema::Column col1{"id", "integer", false, {}, {}, "Primary key"};
    SourceSchema::Column col2{"name", "string", true, 255, {}, "Name field"};
    schema.columns = {col1, col2};
    schema.primary_keys = {"id"};

    EXPECT_CALL(*extractor_ptr_, getSchema())
        .WillOnce(Return(schema));

    auto retrieved_schema = extractor_ptr_->getSchema();
    EXPECT_EQ("test_source", retrieved_schema.source_name);
    EXPECT_EQ(2, retrieved_schema.columns.size());
    EXPECT_EQ("id", retrieved_schema.columns[0].name);
    EXPECT_FALSE(retrieved_schema.columns[0].nullable);
}

// Tests statistics collection
TEST_F(ExtractorBaseTest, Statistics) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Initialize
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    // Extract some records
    std::vector<Record> mock_records;
    for (int i = 0; i < 5; ++i) {
        mock_records.push_back(Record());
    }

    EXPECT_CALL(*extractor_ptr_, extractBatchImpl(_))
        .WillOnce(Return(mock_records));

    auto result = extractor_ptr_->extract_batch(10, context);

    auto stats = extractor_ptr_->get_statistics();
    EXPECT_FALSE(stats.empty()); // Should have some statistics
}

// Tests hasMore functionality
TEST_F(ExtractorBaseTest, HasMore) {
    std::unordered_map<std::string, std::any> config;
    ProcessingContext context;

    // Before initialization
    EXPECT_FALSE(extractor_ptr_->hasMoreData());

    // After initialization
    EXPECT_CALL(*extractor_ptr_, connect()).WillOnce(Return(true));
    ValidationResult valid_result;
    EXPECT_CALL(*extractor_ptr_, validateSource()).WillOnce(Return(valid_result));
    extractor_ptr_->initialize(config, context);

    EXPECT_TRUE(extractor_ptr_->hasMoreData());
}

// Tests ExtractorFactory
TEST(ExtractorFactoryTest, RegisterAndCreate) {
    // Clear any existing registrations
    ExtractorFactory::getRegisteredTypes(); // Initialize if needed

    // Register a test extractor type
    ExtractorFactory::registerExtractor("test_type",
        [](const std::string& name,
           std::shared_ptr<ConfigurationManager> config,
           std::shared_ptr<Logger> logger) {
            return std::make_unique<MockExtractor>(name, config, logger);
        });

    // Create extractor
    auto config = std::make_shared<ConfigurationManager>();
    auto logger = Logger::get("test");
    auto extractor = ExtractorFactory::createExtractor("test_type", "test_instance", config, logger);

    ASSERT_NE(nullptr, extractor);
    EXPECT_EQ("test_instance", extractor->getName());

    // Test invalid type
    EXPECT_THROW(ExtractorFactory::createExtractor("invalid_type", "test", config, logger),
                 ConfigurationException);
}

// Tests getting registered types
TEST(ExtractorFactoryTest, GetRegisteredTypes) {
    // Register multiple types
    ExtractorFactory::registerExtractor("type1",
        [](const std::string& name,
           std::shared_ptr<ConfigurationManager> config,
           std::shared_ptr<Logger> logger) {
            return std::make_unique<MockExtractor>(name, config, logger);
        });

    ExtractorFactory::registerExtractor("type2",
        [](const std::string& name,
           std::shared_ptr<ConfigurationManager> config,
           std::shared_ptr<Logger> logger) {
            return std::make_unique<MockExtractor>(name, config, logger);
        });

    auto types = ExtractorFactory::getRegisteredTypes();
    EXPECT_GE(types.size(), 2);

    // Check that our types are registered
    bool found_type1 = std::find(types.begin(), types.end(), "type1") != types.end();
    bool found_type2 = std::find(types.begin(), types.end(), "type2") != types.end();

    EXPECT_TRUE(found_type1);
    EXPECT_TRUE(found_type2);
}