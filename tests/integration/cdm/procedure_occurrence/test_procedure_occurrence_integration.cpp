// Integration tests for Procedure Occurrence table covering various procedure types, modifiers, and temporal aspects

#include <gtest/gtest.h>
#include <memory>
#include "cdm/omop_tables.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"

namespace omop::test::integration {

class ProcedureOccurrenceIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        db_fixture_ = std::make_unique<DatabaseFixture>();
        db_fixture_->setup_test_database();
        
        // Create prerequisite data
        createPrerequisiteData();
        
        // Initialize transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
        std::unordered_map<std::string, std::any> config{
            {"target_table", "procedure_occurrence"},
            {"vocabulary_path", getTestVocabularyPath()},
            {"enable_validation", true}
        };
        
        core::ProcessingContext context;
        engine_->initialize(config, context);
        
        // Initialize database loader
        loader_ = std::make_unique<load::OmopDatabaseLoader>(
            db_fixture_->create_connection(),
            load::DatabaseLoaderOptions{.batch_size = 100}
        );
    }

    void TearDown() override {
        loader_.reset();
        engine_.reset();
        db_fixture_->cleanup_test_database();
        IntegrationTestBase::TearDown();
    }

private:
    void createPrerequisiteData() {
        // Create persons
        for (int i = 1; i <= 25; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.person (person_id, gender_concept_id, "
                "year_of_birth, race_concept_id, ethnicity_concept_id) "
                "VALUES (" + std::to_string(i) + ", 8507, 1980, 8527, 38003564)"
            );
        }
        
        // Create visits
        for (int i = 1; i <= 25; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.visit_occurrence (visit_occurrence_id, person_id, "
                "visit_concept_id, visit_start_date, visit_end_date, "
                "visit_type_concept_id) "
                "VALUES (" + std::to_string(i * 100) + ", " + std::to_string(i) + 
                ", 9201, '2023-01-01', '2023-01-10', 44818517)" // Inpatient visits
            );
        }
        
        // Create providers
        db_fixture_->execute_update(
            "INSERT INTO cdm.provider (provider_id, provider_name, specialty_concept_id) "
            "VALUES (1, 'Dr. Johnson', 38004456)" // Surgery
        );
        
        db_fixture_->execute_update(
            "INSERT INTO cdm.provider (provider_id, provider_name, specialty_concept_id) "
            "VALUES (2, 'Dr. Williams', 38004458)" // Radiology
        );
    }

protected:
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::unique_ptr<transform::TransformationEngine> engine_;
    std::unique_ptr<load::OmopDatabaseLoader> loader_;
};

// Tests different procedure types (surgical, diagnostic, therapeutic)
TEST_F(ProcedureOccurrenceIntegrationTest, TestProcedureTypes) {
    std::vector<std::map<std::string, std::string>> procedures = {
        // Surgical procedure - Appendectomy
        {{"procedure_occurrence_id", "1001"}, {"person_id", "1"}, 
         {"visit_occurrence_id", "100"},
         {"procedure_concept_id", "4219683"}, // Appendectomy
         {"procedure_date", "2023-01-05"}, 
         {"procedure_type_concept_id", "38000251"}, // Primary procedure
         {"procedure_source_code", "44970"}, {"procedure_source_value", "Laparoscopic appendectomy"},
         {"provider_id", "1"}, {"quantity", "1"}},
        
        // Diagnostic procedure - MRI
        {{"procedure_occurrence_id", "1002"}, {"person_id", "2"},
         {"visit_occurrence_id", "200"},
         {"procedure_concept_id", "4013636"}, // MRI of brain
         {"procedure_date", "2023-01-03"},
         {"procedure_type_concept_id", "38000269"}, // Diagnostic procedure
         {"procedure_source_code", "70551"}, {"procedure_source_value", "MRI brain without contrast"},
         {"provider_id", "2"}},
        
        // Therapeutic procedure - Physical therapy
        {{"procedure_occurrence_id", "1003"}, {"person_id", "3"},
         {"visit_occurrence_id", "300"},
         {"procedure_concept_id", "4079989"}, // Physical therapy
         {"procedure_date", "2023-01-07"},
         {"procedure_type_concept_id", "38000267"}, // Therapeutic procedure
         {"procedure_source_code", "97110"}, {"procedure_source_value", "Therapeutic exercises"},
         {"quantity", "15"}}, // 15 sessions
        
        // Laboratory procedure - Blood draw
        {{"procedure_occurrence_id", "1004"}, {"person_id", "4"},
         {"visit_occurrence_id", "400"},
         {"procedure_concept_id", "4275491"}, // Venipuncture
         {"procedure_date", "2023-01-02"},
         {"procedure_type_concept_id", "38000275"}, // Lab procedure
         {"procedure_source_code", "36415"}, {"procedure_source_value", "Venipuncture"}},
        
        // Emergency procedure - CPR
        {{"procedure_occurrence_id", "1005"}, {"person_id", "5"},
         {"visit_occurrence_id", "500"},
         {"procedure_concept_id", "4210145"}, // Cardiopulmonary resuscitation
         {"procedure_date", "2023-01-01"},
         {"procedure_datetime", "2023-01-01 02:30:00"},
         {"procedure_type_concept_id", "38000276"}, // Emergency procedure
         {"procedure_source_code", "92950"}, {"procedure_source_value", "CPR"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& proc : procedures) {
        core::Record record;
        for (const auto& [field, value] : proc) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify different procedure types
    auto type_check = db_fixture_->execute_query(
        "SELECT procedure_type_concept_id, COUNT(*) "
        "FROM cdm.procedure_occurrence "
        "WHERE procedure_occurrence_id BETWEEN 1001 AND 1005 "
        "GROUP BY procedure_type_concept_id"
    );
    
    EXPECT_EQ(type_check.size(), 5) << "Should have 5 different procedure types";
    
    // Verify emergency procedure has datetime precision
    auto datetime_check = db_fixture_->execute_query(
        "SELECT procedure_datetime FROM cdm.procedure_occurrence "
        "WHERE procedure_occurrence_id = 1005"
    );
    
    ASSERT_EQ(datetime_check.size(), 1);
    EXPECT_TRUE(datetime_check[0][0].find("02:30") != std::string::npos)
        << "Emergency procedure should have time component";
}

// Tests procedures with modifiers
TEST_F(ProcedureOccurrenceIntegrationTest, TestProcedureModifiers) {
    std::vector<std::map<std::string, std::string>> procedures = {
        // Bilateral procedure
        {{"procedure_occurrence_id", "2001"}, {"person_id", "6"},
         {"visit_occurrence_id", "600"},
         {"procedure_concept_id", "4169103"}, // Knee replacement
         {"procedure_date", "2023-02-01"},
         {"procedure_type_concept_id", "38000251"},
         {"modifier_concept_id", "4305677"}, // Bilateral procedure
         {"modifier_source_value", "50"}},
        
        // Left side procedure
        {{"procedure_occurrence_id", "2002"}, {"person_id", "7"},
         {"visit_occurrence_id", "700"},
         {"procedure_concept_id", "4067458"}, // Cataract surgery
         {"procedure_date", "2023-02-05"},
         {"procedure_type_concept_id", "38000251"},
         {"modifier_concept_id", "4262044"}, // Left
         {"modifier_source_value", "LT"}},
        
        // Right side procedure
        {{"procedure_occurrence_id", "2003"}, {"person_id", "8"},
         {"visit_occurrence_id", "800"},
         {"procedure_concept_id", "4067458"}, // Cataract surgery
         {"procedure_date", "2023-02-10"},
         {"procedure_type_concept_id", "38000251"},
         {"modifier_concept_id", "4262045"}, // Right
         {"modifier_source_value", "RT"}},
        
        // Repeat procedure
        {{"procedure_occurrence_id", "2004"}, {"person_id", "9"},
         {"visit_occurrence_id", "900"},
         {"procedure_concept_id", "4013636"}, // MRI
         {"procedure_date", "2023-02-15"},
         {"procedure_type_concept_id", "38000269"},
         {"modifier_concept_id", "4305905"}, // Repeat procedure
         {"modifier_source_value", "76"}},
        
        // Reduced services
        {{"procedure_occurrence_id", "2005"}, {"person_id", "10"},
         {"visit_occurrence_id", "1000"},
         {"procedure_concept_id", "4219683"}, // Appendectomy
         {"procedure_date", "2023-02-20"},
         {"procedure_type_concept_id", "38000251"},
         {"modifier_concept_id", "4262034"}, // Reduced services
         {"modifier_source_value", "52"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& proc : procedures) {
        core::Record record;
        for (const auto& [field, value] : proc) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify modifiers
    auto modifier_check = db_fixture_->execute_query(
        "SELECT procedure_concept_id, modifier_concept_id, modifier_source_value "
        "FROM cdm.procedure_occurrence "
        "WHERE procedure_occurrence_id BETWEEN 2001 AND 2005 "
        "AND modifier_concept_id IS NOT NULL "
        "ORDER BY procedure_occurrence_id"
    );
    
    ASSERT_EQ(modifier_check.size(), 5);
    for (const auto& row : modifier_check) {
        EXPECT_GT(std::stoi(row[1]), 0) << "Should have valid modifier concept";
        EXPECT_FALSE(row[2].empty()) << "Should have modifier source value";
    }
    
    // Verify bilateral procedures
    auto bilateral_check = db_fixture_->execute_query(
        "SELECT COUNT(*) FROM cdm.procedure_occurrence "
        "WHERE modifier_concept_id = 4305677" // Bilateral
    );
    
    EXPECT_EQ(bilateral_check[0][0], "1");
}

// Tests multi-day procedures
TEST_F(ProcedureOccurrenceIntegrationTest, TestMultiDayProcedures) {
    std::vector<std::map<std::string, std::string>> procedures = {
        // Single day procedure
        {{"procedure_occurrence_id", "3001"}, {"person_id", "11"},
         {"visit_occurrence_id", "1100"},
         {"procedure_concept_id", "4219683"}, // Appendectomy
         {"procedure_date", "2023-03-01"},
         {"procedure_type_concept_id", "38000251"}},
        
        // Multi-day procedure - Chemotherapy course
        {{"procedure_occurrence_id", "3002"}, {"person_id", "12"},
         {"visit_occurrence_id", "1200"},
         {"procedure_concept_id", "4179071"}, // Chemotherapy
         {"procedure_date", "2023-03-01"},
         {"procedure_end_date", "2023-03-05"},
         {"procedure_type_concept_id", "38000267"},
         {"quantity", "5"}}, // 5 days
        
        // Multi-day with datetime precision
        {{"procedure_occurrence_id", "3003"}, {"person_id", "13"},
         {"visit_occurrence_id", "1300"},
         {"procedure_concept_id", "4044814"}, // Mechanical ventilation
         {"procedure_date", "2023-03-10"},
         {"procedure_datetime", "2023-03-10 08:00:00"},
         {"procedure_end_date", "2023-03-15"},
         {"procedure_end_datetime", "2023-03-15 14:00:00"},
         {"procedure_type_concept_id", "38000267"}},
        
        // Long-term procedure - Dialysis
        {{"procedure_occurrence_id", "3004"}, {"person_id", "14"},
         {"visit_occurrence_id", "1400"},
         {"procedure_concept_id", "4032243"}, // Hemodialysis
         {"procedure_date", "2023-03-01"},
         {"procedure_end_date", "2023-03-31"},
         {"procedure_type_concept_id", "38000267"},
         {"quantity", "12"}} // 12 sessions
    };
    
    core::ProcessingContext context;
    
    for (const auto& proc : procedures) {
        core::Record record;
        for (const auto& [field, value] : proc) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify procedure durations
    auto duration_check = db_fixture_->execute_query(
        "SELECT procedure_occurrence_id, "
        "COALESCE(procedure_end_date, procedure_date) - procedure_date + 1 as duration_days "
        "FROM cdm.procedure_occurrence "
        "WHERE procedure_occurrence_id BETWEEN 3001 AND 3004 "
        "ORDER BY procedure_occurrence_id"
    );
    
    ASSERT_EQ(duration_check.size(), 4);
    EXPECT_EQ(duration_check[0][1], "1");  // Single day
    EXPECT_EQ(duration_check[1][1], "5");  // 5 days
    EXPECT_EQ(duration_check[2][1], "6");  // 6 days
    EXPECT_EQ(duration_check[3][1], "31"); // Month-long
    
    // Verify datetime precision for ventilation
    auto ventilation_check = db_fixture_->execute_query(
        "SELECT procedure_datetime, procedure_end_datetime "
        "FROM cdm.procedure_occurrence WHERE procedure_occurrence_id = 3003"
    );
    
    ASSERT_EQ(ventilation_check.size(), 1);
    EXPECT_TRUE(ventilation_check[0][0].find("08:00") != std::string::npos);
    EXPECT_TRUE(ventilation_check[0][1].find("14:00") != std::string::npos);
}

// Tests procedure quantities and units
TEST_F(ProcedureOccurrenceIntegrationTest, TestProcedureQuantities) {
    std::vector<std::map<std::string, std::string>> procedures = {
        // Single procedure
        {{"procedure_occurrence_id", "4001"}, {"person_id", "15"},
         {"visit_occurrence_id", "1500"},
         {"procedure_concept_id", "4219683"}, // Appendectomy
         {"procedure_date", "2023-04-01"},
         {"procedure_type_concept_id", "38000251"},
         {"quantity", "1"}},
        
        // Multiple sutures
        {{"procedure_occurrence_id", "4002"}, {"person_id", "16"},
         {"visit_occurrence_id", "1600"},
         {"procedure_concept_id", "4244107"}, // Suture of wound
         {"procedure_date", "2023-04-05"},
         {"procedure_type_concept_id", "38000251"},
         {"quantity", "15"}}, // 15 sutures
        
        // Radiation therapy sessions
        {{"procedure_occurrence_id", "4003"}, {"person_id", "17"},
         {"visit_occurrence_id", "1700"},
         {"procedure_concept_id", "4239647"}, // Radiation therapy
         {"procedure_date", "2023-04-01"},
         {"procedure_end_date", "2023-04-30"},
         {"procedure_type_concept_id", "38000267"},
         {"quantity", "20"}}, // 20 sessions
        
        // Physical therapy sessions
        {{"procedure_occurrence_id", "4004"}, {"person_id", "18"},
         {"visit_occurrence_id", "1800"},
         {"procedure_concept_id", "4079989"}, // Physical therapy
         {"procedure_date", "2023-04-01"},
         {"procedure_type_concept_id", "38000267"},
         {"quantity", "0"}}, // Invalid quantity
        
        // Blood units transfused
        {{"procedure_occurrence_id", "4005"}, {"person_id", "19"},
         {"visit_occurrence_id", "1900"},
         {"procedure_concept_id", "4222586"}, // Blood transfusion
         {"procedure_date", "2023-04-10"},
         {"procedure_type_concept_id", "38000267"},
         {"quantity", "3"}} // 3 units
    };
    
    core::ProcessingContext context;
    
    for (const auto& proc : procedures) {
        core::Record record;
        for (const auto& [field, value] : proc) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify quantities
    auto quantity_check = db_fixture_->execute_query(
        "SELECT procedure_occurrence_id, procedure_source_value, quantity "
        "FROM cdm.procedure_occurrence "
        "WHERE procedure_occurrence_id BETWEEN 4001 AND 4005 "
        "ORDER BY procedure_occurrence_id"
    );
    
    ASSERT_EQ(quantity_check.size(), 5);
    EXPECT_EQ(quantity_check[0][2], "1");  // Single procedure
    EXPECT_EQ(quantity_check[1][2], "15"); // 15 sutures
    EXPECT_EQ(quantity_check[2][2], "20"); // 20 radiation sessions
    EXPECT_TRUE(quantity_check[3][2] == "0" || quantity_check[3][2].empty()); // Invalid/null
    EXPECT_EQ(quantity_check[4][2], "3");  // 3 blood units
    
    // Verify high-quantity procedures
    auto high_quantity = db_fixture_->execute_query(
        "SELECT COUNT(*) FROM cdm.procedure_occurrence "
        "WHERE quantity > 10 AND procedure_occurrence_id BETWEEN 4001 AND 4005"
    );
    
    EXPECT_EQ(high_quantity[0][0], "2") << "Should have 2 procedures with quantity > 10";
}

// Tests procedure type concept mappings
TEST_F(ProcedureOccurrenceIntegrationTest, TestProcedureTypeConcepts) {
    struct ProcedureTypeTestCase {
        std::string procedure_type_source;
        int32_t expected_concept_id;
    };
    
    std::vector<ProcedureTypeTestCase> test_cases = {
        {"Primary procedure", 38000251},
        {"Secondary procedure", 38000252},
        {"Patient reported", 38000268},
        {"Diagnostic procedure", 38000269},
        {"Therapeutic procedure", 38000267},
        {"Lab procedure", 38000275},
        {"Emergency procedure", 38000276}
    };
    
    core::ProcessingContext context;
    
    for (size_t i = 0; i < test_cases.size(); ++i) {
        core::Record record;
        record.setField("procedure_occurrence_id", std::to_string(5000 + i));
        record.setField("person_id", "20");
        record.setField("procedure_concept_id", "4013636"); // Generic procedure
        record.setField("procedure_date", "2023-05-01");
        record.setField("procedure_type", test_cases[i].procedure_type_source);
        record.setField("procedure_source_value", "Test procedure");
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify type concept mappings
    for (size_t i = 0; i < test_cases.size(); ++i) {
        auto result = db_fixture_->execute_query(
            "SELECT procedure_type_concept_id "
            "FROM cdm.procedure_occurrence WHERE procedure_occurrence_id = " + 
            std::to_string(5000 + i)
        );
        
        ASSERT_EQ(result.size(), 1);
        EXPECT_EQ(std::stoi(result[0][0]), test_cases[i].expected_concept_id)
            << "Incorrect mapping for: " << test_cases[i].procedure_type_source;
    }
}

// Tests procedures with provider and visit associations
TEST_F(ProcedureOccurrenceIntegrationTest, TestProviderVisitAssociations) {
    std::vector<std::map<std::string, std::string>> procedures = {
        // Surgical procedure with surgeon
        {{"procedure_occurrence_id", "6001"}, {"person_id", "21"},
         {"visit_occurrence_id", "2100"},
         {"procedure_concept_id", "4219683"}, // Appendectomy
         {"procedure_date", "2023-06-01"},
         {"procedure_type_concept_id", "38000251"},
         {"provider_id", "1"}}, // Surgeon
        
        // Diagnostic procedure with radiologist
        {{"procedure_occurrence_id", "6002"}, {"person_id", "22"},
         {"visit_occurrence_id", "2200"},
         {"procedure_concept_id", "4013636"}, // MRI
         {"procedure_date", "2023-06-05"},
         {"procedure_type_concept_id", "38000269"},
         {"provider_id", "2"}}, // Radiologist
        
        // Procedure without provider
        {{"procedure_occurrence_id", "6003"}, {"person_id", "23"},
         {"visit_occurrence_id", "2300"},
         {"procedure_concept_id", "4275491"}, // Venipuncture
         {"procedure_date", "2023-06-10"},
         {"procedure_type_concept_id", "38000275"}},
        
        // Procedure without visit (outpatient)
        {{"procedure_occurrence_id", "6004"}, {"person_id", "24"},
         {"procedure_concept_id", "4079989"}, // Physical therapy
         {"procedure_date", "2023-06-15"},
         {"procedure_type_concept_id", "38000267"},
         {"provider_id", "1"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& proc : procedures) {
        core::Record record;
        for (const auto& [field, value] : proc) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify provider associations
    auto provider_check = db_fixture_->execute_query(
        "SELECT po.procedure_occurrence_id, po.provider_id, p.specialty_concept_id "
        "FROM cdm.procedure_occurrence po "
        "LEFT JOIN cdm.provider p ON po.provider_id = p.provider_id "
        "WHERE po.procedure_occurrence_id BETWEEN 6001 AND 6004 "
        "ORDER BY po.procedure_occurrence_id"
    );
    
    ASSERT_EQ(provider_check.size(), 4);
    EXPECT_EQ(provider_check[0][2], "38004456"); // Surgery specialty
    EXPECT_EQ(provider_check[1][2], "38004458"); // Radiology specialty
    EXPECT_TRUE(provider_check[2][1].empty() || provider_check[2][1] == "NULL");
    
    // Verify visit associations
    auto visit_check = db_fixture_->execute_query(
        "SELECT COUNT(*) as with_visit, "
        "SUM(CASE WHEN visit_occurrence_id IS NULL THEN 1 ELSE 0 END) as without_visit "
        "FROM cdm.procedure_occurrence "
        "WHERE procedure_occurrence_id BETWEEN 6001 AND 6004"
    );
    
    ASSERT_EQ(visit_check.size(), 1);
    EXPECT_EQ(visit_check[0][0], "4"); // Total procedures
    EXPECT_EQ(visit_check[0][1], "1"); // One without visit
}

// Tests procedure source value mappings
TEST_F(ProcedureOccurrenceIntegrationTest, TestProcedureSourceMappings) {
    std::vector<std::map<std::string, std::string>> procedures = {
        // CPT code mapping
        {{"procedure_occurrence_id", "7001"}, {"person_id", "25"},
         {"procedure_source_code", "99213"}, {"procedure_source_value", "Office visit, established patient"},
         {"procedure_date", "2023-07-01"}, {"procedure_type_concept_id", "38000269"}},
        
        // ICD-10-PCS mapping
        {{"procedure_occurrence_id", "7002"}, {"person_id", "25"},
         {"procedure_source_code", "0FT44ZZ"}, {"procedure_source_value", "Resection of gallbladder"},
         {"procedure_date", "2023-07-05"}, {"procedure_type_concept_id", "38000251"}},
        
        // HCPCS mapping
        {{"procedure_occurrence_id", "7003"}, {"person_id", "25"},
         {"procedure_source_code", "J0585"}, {"procedure_source_value", "Injection, onabotulinumtoxinA"},
         {"procedure_date", "2023-07-10"}, {"procedure_type_concept_id", "38000267"}},
        
        // Custom/local code
        {{"procedure_occurrence_id", "7004"}, {"person_id", "25"},
         {"procedure_source_code", "HOSP-001"}, {"procedure_source_value", "Hospital custom procedure"},
         {"procedure_source_concept_id", "0"}, // No standard mapping
         {"procedure_date", "2023-07-15"}, {"procedure_type_concept_id", "38000251"}},
        
        // SNOMED mapping
        {{"procedure_occurrence_id", "7005"}, {"person_id", "25"},
         {"procedure_source_code", "80146002"}, {"procedure_source_value", "Excision of appendix"},
         {"procedure_date", "2023-07-20"}, {"procedure_type_concept_id", "38000251"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& proc : procedures) {
        core::Record record;
        for (const auto& [field, value] : proc) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify source mappings
    auto mapping_check = db_fixture_->execute_query(
        "SELECT procedure_source_code, procedure_source_value, "
        "procedure_concept_id, procedure_source_concept_id "
        "FROM cdm.procedure_occurrence "
        "WHERE procedure_occurrence_id BETWEEN 7001 AND 7005 "
        "ORDER BY procedure_occurrence_id"
    );
    
    ASSERT_EQ(mapping_check.size(), 5);
    
    // All should have source code and value preserved
    for (const auto& row : mapping_check) {
        EXPECT_FALSE(row[0].empty()) << "Source code should be preserved";
        EXPECT_FALSE(row[1].empty()) << "Source value should be preserved";
    }
    
    // Verify custom code has no standard mapping
    EXPECT_TRUE(mapping_check[3][2] == "0" || mapping_check[3][3] == "0")
        << "Custom code should map to concept 0";
}

// Tests bulk procedure loading performance
TEST_F(ProcedureOccurrenceIntegrationTest, TestBulkProcedureLoading) {
    const size_t batch_size = 1000;
    core::ProcessingContext context;
    
    // Generate bulk procedures
    for (size_t i = 0; i < batch_size; ++i) {
        core::Record record;
        record.setField("procedure_occurrence_id", std::to_string(10000 + i));
        record.setField("person_id", std::to_string((i % 25) + 1));
        record.setField("visit_occurrence_id", std::to_string(((i % 25) + 1) * 100));
        
        // Vary procedure types
        std::string procedure_concept_id;
        if (i % 4 == 0) procedure_concept_id = "4219683"; // Surgical
        else if (i % 4 == 1) procedure_concept_id = "4013636"; // Diagnostic
        else if (i % 4 == 2) procedure_concept_id = "4079989"; // Therapeutic
        else procedure_concept_id = "4275491"; // Lab
        
        record.setField("procedure_concept_id", procedure_concept_id);
        record.setField("procedure_date", "2023-08-01");
        record.setField("procedure_type_concept_id", "38000251");
        record.setField("procedure_source_value", "BULK_" + std::to_string(i));
        
        if (i % 5 == 0) {
            record.setField("quantity", std::to_string((i % 10) + 1));
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        loader_->load(*transformed, context);
    }
    
    auto start_time = std::chrono::steady_clock::now();
    loader_->commit(context);
    auto end_time = std::chrono::steady_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Verify all records loaded
    auto count_result = db_fixture_->execute_query(
        "SELECT COUNT(*), COUNT(DISTINCT procedure_concept_id), COUNT(quantity) "
        "FROM cdm.procedure_occurrence WHERE procedure_occurrence_id >= 10000"
    );
    
    ASSERT_EQ(count_result.size(), 1);
    EXPECT_EQ(std::stoi(count_result[0][0]), batch_size);
    EXPECT_EQ(std::stoi(count_result[0][1]), 4) << "Should have 4 different procedure types";
    EXPECT_EQ(std::stoi(count_result[0][2]), batch_size / 5) << "20% should have quantity";
    
    // Performance assertion
    EXPECT_LT(duration.count(), 5000) 
        << "Bulk loading took " << duration.count() << "ms";
}

// Tests procedure validation and constraints
TEST_F(ProcedureOccurrenceIntegrationTest, TestProcedureValidation) {
    core::ProcessingContext context;
    
    // Test 1: Missing required fields
    {
        core::Record record;
        record.setField("procedure_occurrence_id", "9001");
        record.setField("person_id", "1");
        // Missing procedure_concept_id and procedure_date
        record.setField("procedure_type_concept_id", "38000251");
        
        auto transformed = engine_->transform(record, context);
        EXPECT_FALSE(transformed.has_value()) 
            << "Should reject procedure with missing required fields";
    }
    
    // Test 2: End date before start date
    {
        core::Record record;
        record.setField("procedure_occurrence_id", "9002");
        record.setField("person_id", "1");
        record.setField("procedure_concept_id", "4013636");
        record.setField("procedure_date", "2023-01-10");
        record.setField("procedure_end_date", "2023-01-05"); // Invalid
        record.setField("procedure_type_concept_id", "38000251");
        
        auto transformed = engine_->transform(record, context);
        EXPECT_FALSE(transformed.has_value()) 
            << "Should reject procedure with end date before start date";
    }
    
    // Test 3: Invalid person reference
    {
        core::Record record;
        record.setField("procedure_occurrence_id", "9003");
        record.setField("person_id", "99999"); // Non-existent
        record.setField("procedure_concept_id", "4013636");
        record.setField("procedure_date", "2023-01-01");
        record.setField("procedure_type_concept_id", "38000251");
        
        auto transformed = engine_->transform(record, context);
        if (transformed.has_value()) {
            EXPECT_FALSE(loader_->load(*transformed, context)) 
                << "Should fail foreign key constraint";
        }
    }
    
    // Test 4: Negative quantity
    {
        core::Record record;
        record.setField("procedure_occurrence_id", "9004");
        record.setField("person_id", "1");
        record.setField("procedure_concept_id", "4013636");
        record.setField("procedure_date", "2023-01-01");
        record.setField("procedure_type_concept_id", "38000251");
        record.setField("quantity", "-5"); // Invalid
        
        auto transformed = engine_->transform(record, context);
        EXPECT_FALSE(transformed.has_value()) 
            << "Should reject procedure with negative quantity";
    }
    
    // Test 5: Future procedure date
    {
        core::Record record;
        record.setField("procedure_occurrence_id", "9005");
        record.setField("person_id", "1");
        record.setField("procedure_concept_id", "4013636");
        record.setField("procedure_date", "2099-01-01"); // Future
        record.setField("procedure_type_concept_id", "38000251");
        
        auto transformed = engine_->transform(record, context);
        // This might be allowed depending on business rules
        if (transformed.has_value()) {
            EXPECT_TRUE(loader_->load(*transformed, context));
            loader_->commit(context);
            
            // Verify warning was logged
            EXPECT_GT(context.error_count(), 0) 
                << "Should log warning for future procedure date";
        }
    }
}

// Tests temporal consistency with visits
TEST_F(ProcedureOccurrenceIntegrationTest, TestTemporalConsistencyWithVisits) {
    // Create procedures that should be within visit dates
    std::vector<std::map<std::string, std::string>> procedures = {
        // Procedure within visit dates
        {{"procedure_occurrence_id", "8001"}, {"person_id", "1"},
         {"visit_occurrence_id", "100"},
         {"procedure_concept_id", "4013636"},
         {"procedure_date", "2023-01-05"}, // Within visit (Jan 1-10)
         {"procedure_type_concept_id", "38000251"}},
        
        // Procedure on visit start date
        {{"procedure_occurrence_id", "8002"}, {"person_id", "2"},
         {"visit_occurrence_id", "200"},
         {"procedure_concept_id", "4013636"},
         {"procedure_date", "2023-01-01"}, // Visit start
         {"procedure_type_concept_id", "38000251"}},
        
        // Procedure on visit end date
        {{"procedure_occurrence_id", "8003"}, {"person_id", "3"},
         {"visit_occurrence_id", "300"},
         {"procedure_concept_id", "4013636"},
         {"procedure_date", "2023-01-10"}, // Visit end
         {"procedure_type_concept_id", "38000251"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& proc : procedures) {
        core::Record record;
        for (const auto& [field, value] : proc) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify all procedures are within their visit dates
    auto consistency_check = db_fixture_->execute_query(
        "SELECT COUNT(*) "
        "FROM cdm.procedure_occurrence p "
        "JOIN cdm.visit_occurrence v ON p.visit_occurrence_id = v.visit_occurrence_id "
        "WHERE p.procedure_date < v.visit_start_date "
        "   OR p.procedure_date > v.visit_end_date"
    );
    
    ASSERT_EQ(consistency_check.size(), 1);
    EXPECT_EQ(consistency_check[0][0], "0") 
        << "All procedures should be within their associated visit dates";
}

} // namespace omop::test::integration