// Integration tests for Visit Occurrence table covering visit types, hierarchies, and temporal relationships

#include <gtest/gtest.h>
#include <memory>
#include "cdm/omop_tables.h"
#include "transform/transformation_engine.h"
#include "load/database_loader.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"

namespace omop::test::integration {

class VisitOccurrenceIntegrationTest : public IntegrationTestBase {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        db_fixture_ = std::make_unique<DatabaseFixture>();
        db_fixture_->setup_test_database();
        
        // Create prerequisite data
        createPrerequisiteData();
        
        // Initialize transformation engine
        engine_ = std::make_unique<transform::TransformationEngine>();
        std::unordered_map<std::string, std::any> config{
            {"target_table", "visit_occurrence"},
            {"vocabulary_path", getTestVocabularyPath()},
            {"enable_validation", true}
        };
        
        core::ProcessingContext context;
        engine_->initialize(config, context);
        
        // Initialize database loader
        loader_ = std::make_unique<load::OmopDatabaseLoader>(
            db_fixture_->create_connection(),
            load::DatabaseLoaderOptions{.batch_size = 100}
        );
    }

    void TearDown() override {
        loader_.reset();
        engine_.reset();
        db_fixture_->cleanup_test_database();
        IntegrationTestBase::TearDown();
    }

private:
    void createPrerequisiteData() {
        // Create persons
        for (int i = 1; i <= 30; ++i) {
            db_fixture_->execute_update(
                "INSERT INTO cdm.person (person_id, gender_concept_id, "
                "year_of_birth, race_concept_id, ethnicity_concept_id) "
                "VALUES (" + std::to_string(i) + ", 8507, 1980, 8527, 38003564)"
            );
        }
        
        // Create locations
        db_fixture_->execute_update(
            "INSERT INTO cdm.location (location_id, address_1, city, state, zip) "
            "VALUES (1, '123 Main St', 'Boston', 'MA', '02101')"
        );
        
        // Create care sites
        db_fixture_->execute_update(
            "INSERT INTO cdm.care_site (care_site_id, care_site_name, place_of_service_concept_id, location_id) "
            "VALUES (1, 'General Hospital', 8756, 1)" // Hospital
        );
        
        db_fixture_->execute_update(
            "INSERT INTO cdm.care_site (care_site_id, care_site_name, place_of_service_concept_id, location_id) "
            "VALUES (2, 'Primary Care Clinic', 8940, 1)" // Office
        );
        
        // Create providers
        db_fixture_->execute_update(
            "INSERT INTO cdm.provider (provider_id, provider_name, specialty_concept_id, care_site_id) "
            "VALUES (1, 'Dr. Smith', 38004446, 1)" // Internal Medicine
        );
    }

protected:
    std::unique_ptr<DatabaseFixture> db_fixture_;
    std::unique_ptr<transform::TransformationEngine> engine_;
    std::unique_ptr<load::OmopDatabaseLoader> loader_;
};

// Tests different visit types (inpatient, outpatient, emergency, etc.)
TEST_F(VisitOccurrenceIntegrationTest, TestVisitTypes) {
    std::vector<std::map<std::string, std::string>> visits = {
        // Inpatient visit
        {{"visit_occurrence_id", "1001"}, {"person_id", "1"},
         {"visit_concept_id", "9201"}, // Inpatient visit
         {"visit_start_date", "2023-01-15"}, {"visit_end_date", "2023-01-20"},
         {"visit_type_concept_id", "44818517"}, // Visit derived from encounter on claim
         {"care_site_id", "1"}, {"provider_id", "1"},
         {"visit_source_value", "IP"}, {"visit_source_concept_id", "9201"},
         {"admitted_from_concept_id", "8976"}, // Emergency Room - Hospital
         {"discharged_to_concept_id", "8536"}}, // Home
        
        // Outpatient visit
        {{"visit_occurrence_id", "1002"}, {"person_id", "2"},
         {"visit_concept_id", "9202"}, // Outpatient visit
         {"visit_start_date", "2023-01-10"}, {"visit_end_date", "2023-01-10"},
         {"visit_type_concept_id", "44818517"},
         {"care_site_id", "2"}, {"provider_id", "1"},
         {"visit_source_value", "OP"}},
        
        // Emergency room visit
        {{"visit_occurrence_id", "1003"}, {"person_id", "3"},
         {"visit_concept_id", "9203"}, // Emergency Room visit
         {"visit_start_date", "2023-01-05"}, {"visit_end_date", "2023-01-05"},
         {"visit_type_concept_id", "44818517"},
         {"care_site_id", "1"},
         {"visit_source_value", "ER"},
         {"discharged_to_concept_id", "8536"}}, // Discharged home
        
        // Long term care visit
        {{"visit_occurrence_id", "1004"}, {"person_id", "4"},
         {"visit_concept_id", "42898160"}, // Long Term Care Visit
         {"visit_start_date", "2023-01-01"}, {"visit_end_date", "2023-03-31"},
         {"visit_type_concept_id", "44818517"},
         {"visit_source_value", "LTC"}},
        
        // Telehealth visit
        {{"visit_occurrence_id", "1005"}, {"person_id", "5"},
         {"visit_concept_id", "5083"}, // Telehealth
         {"visit_start_date", "2023-01-12"}, {"visit_end_date", "2023-01-12"},
         {"visit_type_concept_id", "44818517"},
         {"provider_id", "1"},
         {"visit_source_value", "TELE"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& visit : visits) {
        core::Record record;
        for (const auto& [field, value] : visit) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify different visit types
    auto visit_types = db_fixture_->execute_query(
        "SELECT visit_concept_id, COUNT(*) "
        "FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 1001 AND 1005 "
        "GROUP BY visit_concept_id "
        "ORDER BY visit_concept_id"
    );
    
    EXPECT_EQ(visit_types.size(), 5) << "Should have 5 different visit types";
    
    // Verify inpatient visit has admission and discharge info
    auto inpatient_check = db_fixture_->execute_query(
        "SELECT admitted_from_concept_id, discharged_to_concept_id "
        "FROM cdm.visit_occurrence WHERE visit_occurrence_id = 1001"
    );
    
    ASSERT_EQ(inpatient_check.size(), 1);
    EXPECT_GT(std::stoi(inpatient_check[0][0]), 0) << "Should have admission source";
    EXPECT_GT(std::stoi(inpatient_check[0][1]), 0) << "Should have discharge destination";
}

// Tests visit duration calculations and overnight stays
TEST_F(VisitOccurrenceIntegrationTest, TestVisitDurations) {
    std::vector<std::map<std::string, std::string>> visits = {
        // Same-day visit
        {{"visit_occurrence_id", "2001"}, {"person_id", "6"},
         {"visit_concept_id", "9202"}, {"visit_start_date", "2023-02-01"},
         {"visit_end_date", "2023-02-01"}, {"visit_type_concept_id", "44818517"}},
        
        // Multi-day visit
        {{"visit_occurrence_id", "2002"}, {"person_id", "7"},
         {"visit_concept_id", "9201"}, {"visit_start_date", "2023-02-01"},
         {"visit_end_date", "2023-02-05"}, {"visit_type_concept_id", "44818517"}},
        
        // Long-term visit
        {{"visit_occurrence_id", "2003"}, {"person_id", "8"},
         {"visit_concept_id", "42898160"}, {"visit_start_date", "2023-01-01"},
         {"visit_end_date", "2023-06-30"}, {"visit_type_concept_id", "44818517"}},
        
        // Visit with datetime precision
        {{"visit_occurrence_id", "2004"}, {"person_id", "9"},
         {"visit_concept_id", "9203"}, {"visit_start_date", "2023-02-10"},
         {"visit_start_datetime", "2023-02-10 14:30:00"},
         {"visit_end_date", "2023-02-10"},
         {"visit_end_datetime", "2023-02-10 18:45:00"},
         {"visit_type_concept_id", "44818517"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& visit : visits) {
        core::Record record;
        for (const auto& [field, value] : visit) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify visit durations
    auto durations = db_fixture_->execute_query(
        "SELECT visit_occurrence_id, "
        "visit_end_date - visit_start_date + 1 as duration_days "
        "FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 2001 AND 2003 "
        "ORDER BY visit_occurrence_id"
    );
    
    ASSERT_EQ(durations.size(), 3);
    EXPECT_EQ(durations[0][1], "1");   // Same-day = 1 day
    EXPECT_EQ(durations[1][1], "5");   // 5 days
    EXPECT_EQ(durations[2][1], "181"); // 6 months
    
    // Verify datetime precision
    auto datetime_check = db_fixture_->execute_query(
        "SELECT visit_start_datetime, visit_end_datetime "
        "FROM cdm.visit_occurrence WHERE visit_occurrence_id = 2004"
    );
    
    ASSERT_EQ(datetime_check.size(), 1);
    EXPECT_TRUE(datetime_check[0][0].find("14:30") != std::string::npos);
    EXPECT_TRUE(datetime_check[0][1].find("18:45") != std::string::npos);
}

// Tests visit hierarchies and relationships
TEST_F(VisitOccurrenceIntegrationTest, TestVisitHierarchies) {
    // Create parent inpatient visit
    core::Record parent_visit;
    parent_visit.setField("visit_occurrence_id", "3001");
    parent_visit.setField("person_id", "10");
    parent_visit.setField("visit_concept_id", "9201"); // Inpatient
    parent_visit.setField("visit_start_date", "2023-03-01");
    parent_visit.setField("visit_end_date", "2023-03-10");
    parent_visit.setField("visit_type_concept_id", "44818517");
    parent_visit.setField("care_site_id", "1");
    
    core::ProcessingContext context;
    auto transformed = engine_->transform(parent_visit, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    
    // Create preceding visit relationship
    core::Record preceding_visit;
    preceding_visit.setField("visit_occurrence_id", "3002");
    preceding_visit.setField("person_id", "10");
    preceding_visit.setField("visit_concept_id", "9203"); // Emergency
    preceding_visit.setField("visit_start_date", "2023-02-28");
    preceding_visit.setField("visit_end_date", "2023-03-01");
    preceding_visit.setField("visit_type_concept_id", "44818517");
    preceding_visit.setField("preceding_visit_occurrence_id", ""); // No predecessor
    
    transformed = engine_->transform(preceding_visit, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    
    // Create follow-up visit
    core::Record followup_visit;
    followup_visit.setField("visit_occurrence_id", "3003");
    followup_visit.setField("person_id", "10");
    followup_visit.setField("visit_concept_id", "9202"); // Outpatient
    followup_visit.setField("visit_start_date", "2023-03-15");
    followup_visit.setField("visit_end_date", "2023-03-15");
    followup_visit.setField("visit_type_concept_id", "44818517");
    followup_visit.setField("preceding_visit_occurrence_id", "3001");
    
    transformed = engine_->transform(followup_visit, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    
    loader_->commit(context);
    
    // Verify visit sequence
    auto sequence_check = db_fixture_->execute_query(
        "SELECT v1.visit_occurrence_id, v1.visit_concept_id, "
        "v2.visit_occurrence_id as preceding_id, v2.visit_concept_id as preceding_type "
        "FROM cdm.visit_occurrence v1 "
        "LEFT JOIN cdm.visit_occurrence v2 ON v1.preceding_visit_occurrence_id = v2.visit_occurrence_id "
        "WHERE v1.visit_occurrence_id = 3003"
    );
    
    ASSERT_EQ(sequence_check.size(), 1);
    EXPECT_EQ(sequence_check[0][2], "3001") << "Should link to preceding visit";
    
    // Verify chronological order
    auto chronology_check = db_fixture_->execute_query(
        "SELECT visit_occurrence_id, visit_start_date "
        "FROM cdm.visit_occurrence "
        "WHERE person_id = 10 "
        "ORDER BY visit_start_date"
    );
    
    ASSERT_EQ(chronology_check.size(), 3);
    EXPECT_EQ(chronology_check[0][0], "3002"); // Emergency first
    EXPECT_EQ(chronology_check[1][0], "3001"); // Then inpatient
    EXPECT_EQ(chronology_check[2][0], "3003"); // Finally outpatient
}

// Tests overlapping and concurrent visits
TEST_F(VisitOccurrenceIntegrationTest, TestOverlappingVisits) {
    std::vector<std::map<std::string, std::string>> visits = {
        // Primary inpatient visit
        {{"visit_occurrence_id", "4001"}, {"person_id", "11"},
         {"visit_concept_id", "9201"}, {"visit_start_date", "2023-04-01"},
         {"visit_end_date", "2023-04-15"}, {"visit_type_concept_id", "44818517"},
         {"care_site_id", "1"}},
        
        // Overlapping emergency visit during inpatient stay
        {{"visit_occurrence_id", "4002"}, {"person_id", "11"},
         {"visit_concept_id", "9203"}, {"visit_start_date", "2023-04-05"},
         {"visit_end_date", "2023-04-05"}, {"visit_type_concept_id", "44818517"},
         {"care_site_id", "1"}},
        
        // Concurrent outpatient visit
        {{"visit_occurrence_id", "4003"}, {"person_id", "11"},
         {"visit_concept_id", "9202"}, {"visit_start_date", "2023-04-10"},
         {"visit_end_date", "2023-04-10"}, {"visit_type_concept_id", "44818517"},
         {"care_site_id", "2"}},
        
        // Adjacent visit (same end/start date)
        {{"visit_occurrence_id", "4004"}, {"person_id", "11"},
         {"visit_concept_id", "9202"}, {"visit_start_date", "2023-04-15"},
         {"visit_end_date", "2023-04-15"}, {"visit_type_concept_id", "44818517"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& visit : visits) {
        core::Record record;
        for (const auto& [field, value] : visit) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify overlapping visits exist
    auto overlap_check = db_fixture_->execute_query(
        "SELECT v1.visit_occurrence_id, v2.visit_occurrence_id "
        "FROM cdm.visit_occurrence v1 "
        "JOIN cdm.visit_occurrence v2 "
        "  ON v1.person_id = v2.person_id "
        "  AND v1.visit_occurrence_id < v2.visit_occurrence_id "
        "  AND v1.visit_start_date <= v2.visit_end_date "
        "  AND v1.visit_end_date >= v2.visit_start_date "
        "WHERE v1.person_id = 11 "
        "ORDER BY v1.visit_occurrence_id, v2.visit_occurrence_id"
    );
    
    EXPECT_GE(overlap_check.size(), 2) << "Should have overlapping visits";
}

// Tests care site and provider associations
TEST_F(VisitOccurrenceIntegrationTest, TestCareSiteProviderAssociations) {
    std::vector<std::map<std::string, std::string>> visits = {
        // Hospital visit with care site and provider
        {{"visit_occurrence_id", "5001"}, {"person_id", "12"},
         {"visit_concept_id", "9201"}, {"visit_start_date", "2023-05-01"},
         {"visit_end_date", "2023-05-05"}, {"visit_type_concept_id", "44818517"},
         {"care_site_id", "1"}, {"provider_id", "1"}},
        
        // Clinic visit with different care site
        {{"visit_occurrence_id", "5002"}, {"person_id", "13"},
         {"visit_concept_id", "9202"}, {"visit_start_date", "2023-05-10"},
         {"visit_end_date", "2023-05-10"}, {"visit_type_concept_id", "44818517"},
         {"care_site_id", "2"}, {"provider_id", "1"}},
        
        // Visit with care site but no provider
        {{"visit_occurrence_id", "5003"}, {"person_id", "14"},
         {"visit_concept_id", "9202"}, {"visit_start_date", "2023-05-15"},
         {"visit_end_date", "2023-05-15"}, {"visit_type_concept_id", "44818517"},
         {"care_site_id", "2"}},
        
        // Visit with neither care site nor provider
        {{"visit_occurrence_id", "5004"}, {"person_id", "15"},
         {"visit_concept_id", "9202"}, {"visit_start_date", "2023-05-20"},
         {"visit_end_date", "2023-05-20"}, {"visit_type_concept_id", "44818517"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& visit : visits) {
        core::Record record;
        for (const auto& [field, value] : visit) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify care site associations
    auto care_site_check = db_fixture_->execute_query(
        "SELECT v.visit_occurrence_id, v.care_site_id, c.care_site_name "
        "FROM cdm.visit_occurrence v "
        "LEFT JOIN cdm.care_site c ON v.care_site_id = c.care_site_id "
        "WHERE v.visit_occurrence_id BETWEEN 5001 AND 5004 "
        "ORDER BY v.visit_occurrence_id"
    );
    
    ASSERT_EQ(care_site_check.size(), 4);
    EXPECT_EQ(care_site_check[0][2], "General Hospital");
    EXPECT_EQ(care_site_check[1][2], "Primary Care Clinic");
    EXPECT_EQ(care_site_check[2][2], "Primary Care Clinic");
    EXPECT_TRUE(care_site_check[3][1].empty() || care_site_check[3][1] == "NULL");
}

// Tests visit type concept mappings
TEST_F(VisitOccurrenceIntegrationTest, TestVisitTypeConcepts) {
    struct VisitTypeTestCase {
        std::string visit_type_source;
        int32_t expected_concept_id;
    };
    
    std::vector<VisitTypeTestCase> test_cases = {
        {"EHR encounter", 32817},
        {"Claim", 44818517},
        {"Claim primary", 44818516},
        {"Claim secondary", 44818515},
        {"EHR administration", 32827},
        {"EHR billing", 32831},
        {"EHR order", 32833}
    };
    
    core::ProcessingContext context;
    
    for (size_t i = 0; i < test_cases.size(); ++i) {
        core::Record record;
        record.setField("visit_occurrence_id", std::to_string(6000 + i));
        record.setField("person_id", "16");
        record.setField("visit_concept_id", "9202"); // Outpatient
        record.setField("visit_start_date", "2023-06-01");
        record.setField("visit_end_date", "2023-06-01");
        record.setField("visit_type", test_cases[i].visit_type_source);
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify type concept mappings
    for (size_t i = 0; i < test_cases.size(); ++i) {
        auto result = db_fixture_->execute_query(
            "SELECT visit_type_concept_id "
            "FROM cdm.visit_occurrence WHERE visit_occurrence_id = " + std::to_string(6000 + i)
        );
        
        ASSERT_EQ(result.size(), 1);
        EXPECT_EQ(std::stoi(result[0][0]), test_cases[i].expected_concept_id)
            << "Incorrect mapping for: " << test_cases[i].visit_type_source;
    }
}

// Tests admission and discharge concepts
TEST_F(VisitOccurrenceIntegrationTest, TestAdmissionDischargeConcepts) {
    std::vector<std::map<std::string, std::string>> visits = {
        // Emergency admission to ICU discharge
        {{"visit_occurrence_id", "7001"}, {"person_id", "17"},
         {"visit_concept_id", "9201"}, {"visit_start_date", "2023-07-01"},
         {"visit_end_date", "2023-07-10"}, {"visit_type_concept_id", "44818517"},
         {"admitted_from_source_value", "Emergency Room"},
         {"admitted_from_concept_id", "8976"}, // Emergency Room - Hospital
         {"discharged_to_source_value", "Skilled Nursing Facility"},
         {"discharged_to_concept_id", "8863"}}, // Skilled Nursing Facility
        
        // Transfer from another hospital
        {{"visit_occurrence_id", "7002"}, {"person_id", "18"},
         {"visit_concept_id", "9201"}, {"visit_start_date", "2023-07-05"},
         {"visit_end_date", "2023-07-15"}, {"visit_type_concept_id", "44818517"},
         {"admitted_from_source_value", "Transfer from hospital"},
         {"admitted_from_concept_id", "8957"}, // Hospital
         {"discharged_to_source_value", "Home"},
         {"discharged_to_concept_id", "8536"}}, // Home
        
        // Direct admission, AMA discharge
        {{"visit_occurrence_id", "7003"}, {"person_id", "19"},
         {"visit_concept_id", "9201"}, {"visit_start_date", "2023-07-10"},
         {"visit_end_date", "2023-07-12"}, {"visit_type_concept_id", "44818517"},
         {"admitted_from_source_value", "Physician referral"},
         {"admitted_from_concept_id", "8761"}, // Non-hospital institution
         {"discharged_to_source_value", "Left AMA"},
         {"discharged_to_concept_id", "4021968"}}, // Patient discharged alive, left against medical advice
        
        // Expired during visit
        {{"visit_occurrence_id", "7004"}, {"person_id", "20"},
         {"visit_concept_id", "9201"}, {"visit_start_date", "2023-07-15"},
         {"visit_end_date", "2023-07-20"}, {"visit_type_concept_id", "44818517"},
         {"admitted_from_concept_id", "8976"},
         {"discharged_to_source_value", "Expired"},
         {"discharged_to_concept_id", "4216643"}} // Patient died
    };
    
    core::ProcessingContext context;
    
    for (const auto& visit : visits) {
        core::Record record;
        for (const auto& [field, value] : visit) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify admission sources
    auto admission_check = db_fixture_->execute_query(
        "SELECT admitted_from_concept_id, COUNT(*) "
        "FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 7001 AND 7004 "
        "GROUP BY admitted_from_concept_id"
    );
    
    EXPECT_GE(admission_check.size(), 3) << "Should have different admission sources";
    
    // Verify discharge destinations
    auto discharge_check = db_fixture_->execute_query(
        "SELECT discharged_to_concept_id, discharged_to_source_value "
        "FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 7001 AND 7004 "
        "ORDER BY visit_occurrence_id"
    );
    
    ASSERT_EQ(discharge_check.size(), 4);
    for (const auto& row : discharge_check) {
        EXPECT_GT(std::stoi(row[0]), 0) << "Should have valid discharge concept";
    }
}

// Tests bulk visit loading performance
TEST_F(VisitOccurrenceIntegrationTest, TestBulkVisitLoading) {
    const size_t batch_size = 1000;
    core::ProcessingContext context;
    
    // Generate bulk visits
    for (size_t i = 0; i < batch_size; ++i) {
        core::Record record;
        record.setField("visit_occurrence_id", std::to_string(10000 + i));
        record.setField("person_id", std::to_string((i % 30) + 1));
        
        // Vary visit types
        std::string visit_concept_id;
        if (i % 4 == 0) visit_concept_id = "9201"; // Inpatient
        else if (i % 4 == 1) visit_concept_id = "9202"; // Outpatient
        else if (i % 4 == 2) visit_concept_id = "9203"; // Emergency
        else visit_concept_id = "5083"; // Telehealth
        
        record.setField("visit_concept_id", visit_concept_id);
        record.setField("visit_start_date", "2023-08-01");
        record.setField("visit_end_date", "2023-08-01");
        record.setField("visit_type_concept_id", "44818517");
        record.setField("visit_source_value", "BULK_" + std::to_string(i));
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        loader_->load(*transformed, context);
    }
    
    auto start_time = std::chrono::steady_clock::now();
    loader_->commit(context);
    auto end_time = std::chrono::steady_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Verify all records loaded
    auto count_result = db_fixture_->execute_query(
        "SELECT COUNT(*), COUNT(DISTINCT visit_concept_id) "
        "FROM cdm.visit_occurrence WHERE visit_occurrence_id >= 10000"
    );
    
    ASSERT_EQ(count_result.size(), 1);
    EXPECT_EQ(std::stoi(count_result[0][0]), batch_size);
    EXPECT_EQ(std::stoi(count_result[0][1]), 4) << "Should have 4 different visit types";
    
    // Performance assertion
    EXPECT_LT(duration.count(), 5000) 
        << "Bulk loading took " << duration.count() << "ms";
}

// Tests visit data validation and constraints
TEST_F(VisitOccurrenceIntegrationTest, TestVisitValidation) {
    core::ProcessingContext context;
    
    // Test 1: End date before start date
    {
        core::Record record;
        record.setField("visit_occurrence_id", "9001");
        record.setField("person_id", "1");
        record.setField("visit_concept_id", "9201");
        record.setField("visit_start_date", "2023-01-10");
        record.setField("visit_end_date", "2023-01-05"); // Invalid: end before start
        record.setField("visit_type_concept_id", "44818517");
        
        auto transformed = engine_->transform(record, context);
        EXPECT_FALSE(transformed.has_value()) 
            << "Should reject visit with end date before start date";
    }
    
    // Test 2: Missing required fields
    {
        core::Record record;
        record.setField("visit_occurrence_id", "9002");
        record.setField("person_id", "1");
        // Missing visit_concept_id and dates
        record.setField("visit_type_concept_id", "44818517");
        
        auto transformed = engine_->transform(record, context);
        EXPECT_FALSE(transformed.has_value()) 
            << "Should reject visit with missing required fields";
    }
    
    // Test 3: Invalid person reference
    {
        core::Record record;
        record.setField("visit_occurrence_id", "9003");
        record.setField("person_id", "99999"); // Non-existent person
        record.setField("visit_concept_id", "9201");
        record.setField("visit_start_date", "2023-01-01");
        record.setField("visit_end_date", "2023-01-05");
        record.setField("visit_type_concept_id", "44818517");
        
        auto transformed = engine_->transform(record, context);
        if (transformed.has_value()) {
            EXPECT_FALSE(loader_->load(*transformed, context)) 
                << "Should fail foreign key constraint";
        }
    }
    
    // Test 4: Invalid care site reference
    {
        core::Record record;
        record.setField("visit_occurrence_id", "9004");
        record.setField("person_id", "1");
        record.setField("visit_concept_id", "9201");
        record.setField("visit_start_date", "2023-01-01");
        record.setField("visit_end_date", "2023-01-05");
        record.setField("visit_type_concept_id", "44818517");
        record.setField("care_site_id", "99999"); // Non-existent care site
        
        auto transformed = engine_->transform(record, context);
        if (transformed.has_value()) {
            EXPECT_FALSE(loader_->load(*transformed, context)) 
                << "Should fail foreign key constraint for care_site";
        }
    }
    
    // Test 5: Self-referencing preceding visit
    {
        core::Record record;
        record.setField("visit_occurrence_id", "9005");
        record.setField("person_id", "1");
        record.setField("visit_concept_id", "9201");
        record.setField("visit_start_date", "2023-01-01");
        record.setField("visit_end_date", "2023-01-05");
        record.setField("visit_type_concept_id", "44818517");
        record.setField("preceding_visit_occurrence_id", "9005"); // Self-reference
        
        auto transformed = engine_->transform(record, context);
        EXPECT_FALSE(transformed.has_value()) 
            << "Should reject self-referencing preceding visit";
    }
}

// Tests temporal consistency between visits and related events
TEST_F(VisitOccurrenceIntegrationTest, TestTemporalConsistency) {
    // Create a visit
    core::Record visit;
    visit.setField("visit_occurrence_id", "8001");
    visit.setField("person_id", "21");
    visit.setField("visit_concept_id", "9201");
    visit.setField("visit_start_date", "2023-09-01");
    visit.setField("visit_end_date", "2023-09-10");
    visit.setField("visit_type_concept_id", "44818517");
    
    core::ProcessingContext context;
    auto transformed = engine_->transform(visit, context);
    ASSERT_TRUE(transformed.has_value());
    EXPECT_TRUE(loader_->load(*transformed, context));
    loader_->commit(context);
    
    // Create condition that should be within visit dates
    db_fixture_->execute_update(
        "INSERT INTO cdm.condition_occurrence "
        "(condition_occurrence_id, person_id, condition_concept_id, "
        "condition_start_date, condition_type_concept_id, visit_occurrence_id) "
        "VALUES (8001, 21, 320128, '2023-09-05', 38000183, 8001)"
    );
    
    // Verify temporal consistency
    auto consistency_check = db_fixture_->execute_query(
        "SELECT COUNT(*) "
        "FROM cdm.condition_occurrence c "
        "JOIN cdm.visit_occurrence v ON c.visit_occurrence_id = v.visit_occurrence_id "
        "WHERE c.condition_start_date < v.visit_start_date "
        "   OR c.condition_start_date > v.visit_end_date"
    );
    
    ASSERT_EQ(consistency_check.size(), 1);
    EXPECT_EQ(consistency_check[0][0], "0") 
        << "All conditions should be within their associated visit dates";
}

// Tests visit datetime precision and timezone handling
TEST_F(VisitOccurrenceIntegrationTest, TestVisitDateTimePrecision) {
    std::vector<std::map<std::string, std::string>> visits = {
        // Visit with precise start/end times
        {{"visit_occurrence_id", "9001"}, {"person_id", "22"},
         {"visit_concept_id", "9203"}, // ER
         {"visit_start_date", "2023-10-01"},
         {"visit_start_datetime", "2023-10-01 08:30:15"},
         {"visit_end_date", "2023-10-01"},
         {"visit_end_datetime", "2023-10-01 12:45:30"},
         {"visit_type_concept_id", "44818517"}},
        
        // Visit spanning midnight
        {{"visit_occurrence_id", "9002"}, {"person_id", "23"},
         {"visit_concept_id", "9203"}, // ER
         {"visit_start_date", "2023-10-01"},
         {"visit_start_datetime", "2023-10-01 23:30:00"},
         {"visit_end_date", "2023-10-02"},
         {"visit_end_datetime", "2023-10-02 02:15:00"},
         {"visit_type_concept_id", "44818517"}},
        
        // Visit with only dates (no times)
        {{"visit_occurrence_id", "9003"}, {"person_id", "24"},
         {"visit_concept_id", "9202"}, // Outpatient
         {"visit_start_date", "2023-10-05"},
         {"visit_end_date", "2023-10-05"},
         {"visit_type_concept_id", "44818517"}},
        
        // Long visit with datetime precision
        {{"visit_occurrence_id", "9004"}, {"person_id", "25"},
         {"visit_concept_id", "9201"}, // Inpatient
         {"visit_start_date", "2023-10-10"},
         {"visit_start_datetime", "2023-10-10 14:22:45"},
         {"visit_end_date", "2023-10-15"},
         {"visit_end_datetime", "2023-10-15 10:00:00"},
         {"visit_type_concept_id", "44818517"}}
    };
    
    core::ProcessingContext context;
    
    for (const auto& visit : visits) {
        core::Record record;
        for (const auto& [field, value] : visit) {
            record.setField(field, value);
        }
        
        auto transformed = engine_->transform(record, context);
        ASSERT_TRUE(transformed.has_value());
        EXPECT_TRUE(loader_->load(*transformed, context));
    }
    
    loader_->commit(context);
    
    // Verify datetime handling
    auto datetime_check = db_fixture_->execute_query(
        "SELECT visit_occurrence_id, "
        "visit_start_date, visit_start_datetime, "
        "visit_end_date, visit_end_datetime, "
        "EXTRACT(EPOCH FROM (visit_end_datetime - visit_start_datetime))/3600 as duration_hours "
        "FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 9001 AND 9004 "
        "ORDER BY visit_occurrence_id"
    );
    
    ASSERT_EQ(datetime_check.size(), 4);
    
    // Verify ER visit duration (should be about 4.25 hours)
    double er_duration = std::stod(datetime_check[0][5]);
    EXPECT_NEAR(er_duration, 4.25, 0.1);
    
    // Verify midnight-spanning visit
    double midnight_duration = std::stod(datetime_check[1][5]);
    EXPECT_NEAR(midnight_duration, 2.75, 0.1);
    
    // Verify date consistency
    for (const auto& row : datetime_check) {
        // If datetime is provided, date should match
        if (!row[2].empty() && row[2] != "NULL") {
            std::string datetime_date = row[2].substr(0, 10);
            EXPECT_EQ(datetime_date, row[1]) << "Start date should match datetime date part";
        }
    }
}

// Tests batch visit processing with various scenarios
TEST_F(VisitOccurrenceIntegrationTest, TestBatchVisitProcessing) {
    core::ProcessingContext context;
    
    // Generate various visit scenarios
    for (int i = 1; i <= 50; ++i) {
        core::Record record;
        record.setField("visit_occurrence_id", std::to_string(10000 + i));
        record.setField("person_id", std::to_string(1 + (i % 30)));
        
        // Vary visit types
        if (i % 4 == 0) {
            record.setField("visit_concept_id", "9201"); // Inpatient
            record.setField("visit_start_date", "2023-11-01");
            record.setField("visit_end_date", "2023-11-05");
        } else if (i % 4 == 1) {
            record.setField("visit_concept_id", "9202"); // Outpatient
            record.setField("visit_start_date", "2023-11-10");
            record.setField("visit_end_date", "2023-11-10");
        } else if (i % 4 == 2) {
            record.setField("visit_concept_id", "9203"); // ER
            record.setField("visit_start_date", "2023-11-15");
            record.setField("visit_end_date", "2023-11-15");
        } else {
            // Invalid visit - missing required fields
            record.setField("visit_start_date", "2023-11-20");
            // Missing visit_concept_id
        }
        
        record.setField("visit_type_concept_id", "44818517");
        
        // Add some optional fields
        if (i % 3 == 0) {
            record.setField("care_site_id", std::to_string(1 + (i % 2)));
            record.setField("provider_id", "1");
        }
        
        auto transformed = engine_->transform(record, context);
        if (transformed.has_value()) {
            loader_->load(*transformed, context);
        }
    }
    
    loader_->commit(context);
    
    // Verify results
    auto count_result = db_fixture_->execute_query(
        "SELECT COUNT(*) FROM cdm.visit_occurrence WHERE visit_occurrence_id BETWEEN 10001 AND 10050"
    );
    
    ASSERT_EQ(count_result.size(), 1);
    int loaded_count = std::stoi(count_result[0][0]);
    EXPECT_LT(loaded_count, 50) << "Some records should fail validation";
    EXPECT_GT(loaded_count, 35) << "Most records should succeed";
    
    // Check visit type distribution
    auto type_distribution = db_fixture_->execute_query(
        "SELECT visit_concept_id, COUNT(*) as count "
        "FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 10001 AND 10050 "
        "GROUP BY visit_concept_id "
        "ORDER BY visit_concept_id"
    );
    
    EXPECT_EQ(type_distribution.size(), 3) << "Should have 3 valid visit types";
}

// Tests edge cases and error conditions
TEST_F(VisitOccurrenceIntegrationTest, TestVisitEdgeCases) {
    std::vector<std::map<std::string, std::string>> edge_cases = {
        // Visit with end date before start date (should fail)
        {{"visit_occurrence_id", "11001"}, {"person_id", "1"},
         {"visit_concept_id", "9201"},
         {"visit_start_date", "2023-12-10"},
         {"visit_end_date", "2023-12-05"}, // Before start date
         {"visit_type_concept_id", "44818517"}},
        
        // Future visit date (may be valid depending on rules)
        {{"visit_occurrence_id", "11002"}, {"person_id", "2"},
         {"visit_concept_id", "9202"},
         {"visit_start_date", "2025-01-01"},
         {"visit_end_date", "2025-01-01"},
         {"visit_type_concept_id", "44818517"}},
        
        // Very long visit duration
        {{"visit_occurrence_id", "11003"}, {"person_id", "3"},
         {"visit_concept_id", "42898160"}, // Long term care
         {"visit_start_date", "2020-01-01"},
         {"visit_end_date", "2023-12-31"}, // Nearly 4 years
         {"visit_type_concept_id", "44818517"}},
        
        // Visit with invalid person (referential integrity)
        {{"visit_occurrence_id", "11004"}, {"person_id", "999999"},
         {"visit_concept_id", "9202"},
         {"visit_start_date", "2023-12-15"},
         {"visit_end_date", "2023-12-15"},
         {"visit_type_concept_id", "44818517"}},
        
        // Visit with special characters in source value
        {{"visit_occurrence_id", "11005"}, {"person_id", "4"},
         {"visit_concept_id", "9202"},
         {"visit_start_date", "2023-12-20"},
         {"visit_end_date", "2023-12-20"},
         {"visit_type_concept_id", "44818517"},
         {"visit_source_value", "Visit <> with \"special\" & 'chars'"}}
    };
    
    core::ProcessingContext context;
    size_t success_count = 0;
    
    for (const auto& edge_case : edge_cases) {
        core::Record record;
        for (const auto& [field, value] : edge_case) {
            record.setField(field, value);
        }
        
        try {
            auto transformed = engine_->transform(record, context);
            if (transformed.has_value() && loader_->load(*transformed, context)) {
                success_count++;
            }
        } catch (const std::exception& e) {
            // Log warning for edge case failures
            context.log("warning", 
                std::format("Edge case {} failed: {}", 
                          edge_case.at("visit_occurrence_id"), e.what()));
        }
    }
    
    loader_->commit(context);
    
    // Some edge cases should fail
    EXPECT_LT(success_count, edge_cases.size()) 
        << "Some edge cases should fail validation";
    
    // Verify which cases succeeded
    auto loaded_cases = db_fixture_->execute_query(
        "SELECT visit_occurrence_id FROM cdm.visit_occurrence "
        "WHERE visit_occurrence_id BETWEEN 11001 AND 11005"
    );
    
    // Long-term care and special characters should succeed
    bool found_ltc = false;
    bool found_special = false;
    
    for (const auto& row : loaded_cases) {
        if (row[0] == "11003") found_ltc = true;
        if (row[0] == "11005") found_special = true;
    }
    
    EXPECT_TRUE(found_ltc) << "Long-term care visit should be valid";
    EXPECT_TRUE(found_special) << "Special characters should be handled";
}

} // namespace omop::test::integration